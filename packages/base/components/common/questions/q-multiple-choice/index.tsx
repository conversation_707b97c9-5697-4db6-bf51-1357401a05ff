import { computed, defineComponent } from 'vue'
import type { PropType } from 'vue'
import { NButton, NPopconfirm } from 'naive-ui'
import SvgIcon from '@sa/components/custom/svg-icon.vue'
import { useQuestionsForm } from '@sa/hooks'
import type { TransformToVoQuestionData } from '@sa/utils'
import styles from './index.module.scss'

export default defineComponent({
  name: 'MultipleChoice',
  props: {
    item: {
      type: Object as PropType<TransformToVoQuestionData>,
      required: true,
    },
    type: {
      type: String as PropType<'edit' | 'answer' | 'preview'>,
      default: 'answer',
    },
  },
  emits: ['update:item'],
  setup(props, { emit, expose }) {
    // 修改 item 属性的方法
    const updateItemProperty = (property: keyof TransformToVoQuestionData, value: any) => {
      const updatedItem = { ...props.item, [property]: value }
      emit('update:item', updatedItem)
    }
    const isdisabled = computed(() => props.type === 'preview')
    const { mergedDisabled } = useQuestionsForm({
      type: computed(() => props.type),
    })

    // 用户答案，从 item.userAnswer 中获取，多选题应该是数组
    const userAnswer = computed({
      get() {
        return Array.isArray(props.item.userAnswer) ? props.item.userAnswer : []
      },
      set(value: string[]) {
        updateItemProperty('userAnswer', value)
      },
    })
    // 系统答案
    const systemAnswer = computed(() => props.item.correctAnswer?.split(',') || [])
    console.log(props.item)
    const handleChange = (value: string) => {
      if (mergedDisabled.value)
        return
      const currentValues = [...userAnswer.value]
      const selected = currentValues.includes(value)
      if (selected) {
        userAnswer.value = currentValues.filter(item => item !== value)
      }
      else {
        userAnswer.value = [...currentValues, value]
      }
    }

    // 删除选项
    const handleDeleteOption = (optionValue: string) => {
      if (!props.item.options || props.item.options.length <= 2) {
        // 至少保留2个选项
        return
      }

      // 过滤掉要删除的选项
      const filteredOptions = props.item.options.filter((option: any) => option.value !== optionValue)

      // 直接使用过滤后的选项，不重新排序
      updateItemProperty('options', filteredOptions)

      // 处理正确答案的更新 - 简单移除被删除的选项
      if (props.item.correctAnswer && props.item.correctAnswer.includes(optionValue)) {
        const correctAnswers = props.item.correctAnswer.split(',').filter(answer => answer !== optionValue)
        updateItemProperty('correctAnswer', correctAnswers.join(','))
      }

      // 处理用户答案的更新 - 简单移除被删除的选项
      if (Array.isArray(userAnswer.value) && userAnswer.value.includes(optionValue)) {
        userAnswer.value = userAnswer.value.filter(answer => answer !== optionValue)
      }
    }

    // 渲染函数
    const renderContent = () => (
      <ul class={styles.root}>
        {props.item.options?.map((option: any, index: number) => (
          <li key={index}>
            <div class={styles.choiceItemWrapper}>
              <label class={styles.choiceItem}>
                <span>{option.value}</span>
                {systemAnswer.value}
                <div
                  class={[
                    styles.choiceItemQn,
                    !isdisabled && styles.cursor,
                    // systemAnswer.value.includes(option.value) ? styles.choiceItemQnChecked : '',
                  ]}
                  onClick={() => {
                    if (mergedDisabled.value) {
                      return
                    }
                    handleChange(option.value)
                  }}
                >
                  <span>{option.value}</span>
                </div>
                <span
                  class={[styles.choiceItemLabel, 'contents']}
                  v-html={option.label}
                  v-katex
                />
              </label>
              {/* 编辑模式下显示删除按钮 */}
              {props.type === 'edit' && props.item.options && props.item.options.length > 2 && (
                <NPopconfirm
                  onPositiveClick={() => handleDeleteOption(option.value)}
                  positiveText="确认删除"
                  negativeText="取消"
                >
                  {{
                    trigger: () => (
                      <NButton
                        size="small"
                        quaternary={true}
                        class={styles.deleteButton}
                      >
                        <SvgIcon icon="mdi:close" />
                      </NButton>
                    ),
                    default: () => `确定要删除选项 ${option.value} 吗？`,
                  }}
                </NPopconfirm>
              )}
            </div>
          </li>
        ))}
      </ul>
    )

    // 暴露修改 item 属性的方法
    expose({
      updateItemProperty,
    })

    return {
      renderContent,
    }
  },
  render() {
    return this.renderContent()
  },
})
